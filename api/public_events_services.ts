import {
  useMutation,
  useQuery,
  useInfiniteQuery,
  useQueryClient
} from '@tanstack/react-query';
import axiosInstance from './axios_instance';
import { AxiosError } from 'axios';
import { authenticationStore } from '@/stores/authentication_store';
import { 
  fetchIntervalMs, ApiConfig, 
  type EventCheckInRequest,
  type PublishedEventListPullRequest,
  type EventDetailsPullRequest,
  type EventTagsPullRequest,
  type EventListPayload,
  type EventListPayloadDetails,
  type EventTagPayload,
  type EventRegistrationPayload,
} from '@/api/api_config';
import { 
  dashboardEventsListStore, 
  exploreEventsListStore, 
  eventDetailsStore,
  eventTagsStore,
} from 'stores/public_events_store';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';



// Parameter mapping function for events API (only for events, not other APIs)
const mapEventsApiParams = (params?: PublishedEventListPullRequest) => {
  if (!params) return params;
  
  const mappedParams: any = { ...params };
  
  // Map parameter names to match webapp's API format
  if (params.tagIds) {
    mappedParams.tag_ids = Array.isArray(params.tagIds) 
      ? params.tagIds.join(',')  // Convert array to comma-separated string like webapp
      : params.tagIds;
    delete mappedParams.tagIds;
  }
  
  if (params.organization_id) {
    mappedParams.org_id = params.organization_id;
    delete mappedParams.organization_id;
  }
  
  return mappedParams;
};

export const useFetchDashboardEvents = (requestParams?: PublishedEventListPullRequest) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['dashboardEvents', requestParams] as const;
  const queryResult = useQuery<EventListPayload[], Error, EventListPayload[], typeof queryKeyConst>(
    {
      queryKey: queryKeyConst,
      queryFn: async () => {
        try {
          const response = await axiosInstance.request<EventListPayload[]>({
            url: ApiConfig.public_events.published_event_list_pull.endpoint,
            method: ApiConfig.public_events.published_event_list_pull.method,
            params: requestParams,
            headers: { 'Content-Type': 'application/json' }
          });
          return response.data;
        } catch (error) {
          throw error;
        }
      },
    }
  );

  useEffect(() => {
    const store = dashboardEventsListStore.getState();
    if (queryResult.isLoading) {
      // console.log('[public_events_services] useFetchDashboardEvents is fetching');
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as AxiosError);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setDashboardEventsList(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
      // console.log(`[public_events_services] dashboardEventsListStore setDashboardEventsList successful`);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams, queryClient]);

  return queryResult;
};

export const useFetchExploreEvents = (requestParams?: PublishedEventListPullRequest) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['exploreEvents', requestParams] as const;
  const queryResult = useQuery<EventListPayload[], Error, EventListPayload[], typeof queryKeyConst>(
    {
      queryKey: queryKeyConst,
      queryFn: async () => {
        try {
          // Map parameters to match webapp's API format
          const mappedParams = mapEventsApiParams(requestParams);
          
          const response = await axiosInstance.request<EventListPayload[]>({
            url: ApiConfig.public_events.published_event_list_pull.endpoint,
            method: ApiConfig.public_events.published_event_list_pull.method,
            params: mappedParams,
            headers: { 'Content-Type': 'application/json' }
          });
          return response.data;
        } catch (error) {
          throw error;
        }
      },
    }
  );

  useEffect(() => {
    const store = exploreEventsListStore.getState();
    if (queryResult.isLoading) {
      // console.log('[public_events_services] useFetchExploreEvents is fetching');
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as AxiosError);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setExploreEventsList(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
      // console.log(`[public_events_services] exploreEventsListStore setExploreEventsList successful`);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams, queryClient]);

  return queryResult;
};

export const useFetchEventDetails = (requestParams: EventDetailsPullRequest) => {
  const queryClient = useQueryClient();
  const { eventId } = requestParams;
  const queryKeyConst = ['eventDetails', eventId] as const;

  const queryResult = useQuery<EventListPayloadDetails, Error, EventListPayloadDetails, typeof queryKeyConst>(
    {
      queryKey: queryKeyConst,
      queryFn: async () => {
        if (!eventId) throw new Error('[useFetchEventDetails] eventId is required for fetching event details.');
        try {
          const response = await axiosInstance.request<EventListPayloadDetails>({
            url: ApiConfig.public_events.event_details_pull.endpoint.replace('{eventId}', eventId),
            method: ApiConfig.public_events.event_details_pull.method, 
            headers: { 'Content-Type': 'application/json' }
          });
          return response.data;
        } catch (error) {
          throw error;
        }
      },
      enabled: !!eventId,
    }
  );

  useEffect(() => {
    const store = eventDetailsStore.getState();
    if (queryResult.isLoading) {
      // console.log(`[public_events_services] useFetchEventDetails is fetching`);
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as AxiosError);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setEventDetails(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
      // console.log(`[public_events_services] eventDetailsStore setEventDetails successful`);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, eventId, requestParams, queryClient]);

  return queryResult;
};

export const useFetchEventTags = (requestParams?: EventTagsPullRequest) => {
  const queryClient = useQueryClient();
  const { i18n } = useTranslation();
  const queryKeyConst = ['eventTags', requestParams, i18n.language] as const;
  const queryResult = useQuery<EventTagPayload[], Error, EventTagPayload[], typeof queryKeyConst>(
    {
      queryKey: queryKeyConst,
      queryFn: async () => {
        try {
          const response = await axiosInstance.request<EventTagPayload[]>({
            url: ApiConfig.public_events.event_tags_pull.endpoint,
            method: ApiConfig.public_events.event_tags_pull.method,
            params: requestParams,
            headers: { 'Content-Type': 'application/json' }
          });
          return response.data;
        } catch (error) {
          throw error;
        }
      },
    }
  );

  useEffect(() => {
    const store = eventTagsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as AxiosError);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      // API now returns tags with format: { id, name_en, name_zh_hk, name_zh_cn, is_globally_approved }
      // No need to filter by language - all languages are in one response

      // Ensure data is an array before calling filter
      if (Array.isArray(queryResult.data)) {
        const filteredTags = queryResult.data.filter(tag =>
          tag.id &&
          (tag.name_en || tag.name_zh_hk || tag.name_zh_cn)
        );

        store.setEventTags(filteredTags);
      } else {
        // If data is not an array, set empty array and log warning
        console.warn('[useFetchEventTags] API returned non-array data:', queryResult.data);
        store.setEventTags([]);
      }

      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams, queryClient, i18n.language]);

  return queryResult;
};

export const eventCheckIn = () => {
  const queryClient = useQueryClient();
  return useMutation<EventRegistrationPayload, Error, EventCheckInRequest>({
    mutationFn: async (payload: EventCheckInRequest): Promise<EventRegistrationPayload> => {
      // TODO: Add Authorization header if required by your API
      // const token = your_auth_token_source; // e.g., from Zustand store or React context
      // const headers = {
      //   'Content-Type': 'application/json',
      //   'Authorization': `Bearer ${token}`,
      // };

      try {
        const response = await axiosInstance.request<EventRegistrationPayload>({
          url: ApiConfig.user_events.event_check_in.endpoint,
          method: ApiConfig.user_events.event_check_in.method,
          data: payload,
          headers: { 'Content-Type': 'application/json' }
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: EventRegistrationPayload) => {
      // console.log(`[public_events_services] eventCheckIn successful, response: ${data}`);
    },
    onError: (error: Error, variables: EventCheckInRequest) => {
      console.error(`[public_events_services] eventCheckIn error: ${error}, request: ${JSON.stringify(variables)}`);
      // Token handling is now managed by axios interceptor
    },
  });
};